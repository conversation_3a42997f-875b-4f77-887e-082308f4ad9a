#!/usr/bin/env python3
"""
Clean Multi-MCP Client

A clean, well-organized client for connecting to multiple MCP servers simultaneously.
Supports HTTP, SSE, and stdio protocols with improved error handling and code organization.

Features:
    - Clean separation of concerns with dedicated classes
    - Robust connection management and error handling
    - Support for multiple protocols (HTTP, SSE, stdio)
    - Auto-discovery and routing of tool calls
    - Comprehensive logging and debugging
    - Graceful cleanup and resource management

Usage:
    from multi_mcp_client_clean import MultiMCPClient
    
    client = MultiMCPClient()
    await client.add_server("server1", "http://localhost:9000/mcp", "http")
    result = await client.call_tool("server1", "tool_name", {"param": "value"})
"""

import asyncio
import logging
import os
import sys
import traceback
from abc import ABC, abstractmethod
from contextlib import AsyncExitStack, asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from enum import Enum
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Protocol(Enum):
    """Supported MCP protocols."""
    HTTP = "http"
    SSE = "sse"
    STDIO = "stdio"
    
    @classmethod
    def from_string(cls, value: str) -> 'Protocol':
        """Create Protocol from string value."""
        value_lower = value.lower()
        for protocol in cls:
            if protocol.value == value_lower:
                return protocol
        raise ValueError(f"Invalid protocol: {value}")


@dataclass
class ToolCallResult:
    """Result of a tool call with comprehensive information."""
    success: bool
    content: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    server_id: Optional[str] = None
    tool_name: Optional[str] = None
    traceback: Optional[str] = None


@dataclass
class ServerInfo:
    """Information about a connected MCP server."""
    server_id: str
    url: str
    protocol: Protocol
    description: str
    connected_at: datetime
    tools: List[Dict[str, Any]]
    client: 'MCPClientInterface'
    metadata: Dict[str, Any] = field(default_factory=dict)


class MCPClientInterface(ABC):
    """Abstract interface for MCP clients."""
    
    @abstractmethod
    async def connect(self, url: str) -> bool:
        """Connect to the MCP server."""
        pass
    
    @abstractmethod
    async def call_tool(self, tool_name: str, tool_input: Any, tool_call_id: str = "call", timeout: float = 30.0) -> ToolCallResult:
        """Call a tool on the server."""
        pass
    
    @abstractmethod
    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools."""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """Clean up resources."""
        pass
    
    @property
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return False


class HTTPMCPClient(MCPClientInterface):
    """HTTP protocol MCP client wrapper."""
    
    def __init__(self, anthropic_api_key: Optional[str] = None):
        self.anthropic_api_key = anthropic_api_key
        self._client = None
        self._available_tools = []
        self._connected = False
    
    @property
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self._connected and self._client is not None
    
    async def connect(self, url: str) -> bool:
        """Connect to HTTP MCP server."""
        try:
            # Try different import paths
            try:
                from .mcp_http_clientlib import MCPClientLib
            except ImportError:
                try:
                    from mcp_http_clientlib import MCPClientLib
                except ImportError:
                    logger.error("Could not import MCPClientLib for HTTP protocol")
                    return False
            
            self._client = MCPClientLib(
                anthropic_api_key=self.anthropic_api_key,
                debug_callback=self._debug_callback
            )
            
            success = await self._client.connect_to_server(url)
            if success:
                self._available_tools = getattr(self._client, 'available_tools', [])
                self._connected = True
            return success
            
        except Exception as e:
            logger.error(f"HTTP client connection error: {e}")
            logger.debug(traceback.format_exc())
            return False
    
    async def call_tool(self, tool_name: str, tool_input: Any, tool_call_id: str = "call", timeout: float = 30.0) -> ToolCallResult:
        """Call tool via HTTP client."""
        if not self._client:
            return ToolCallResult(
                success=False, 
                error="Client not connected",
                tool_name=tool_name
            )
        
        start_time = time.time()
        try:
            result = await asyncio.wait_for(
                self._client.call_tool(tool_name, tool_input, tool_call_id),
                timeout=timeout
            )
            
            execution_time = time.time() - start_time
            
            # Handle different result formats
            if hasattr(result, 'success'):
                return ToolCallResult(
                    success=result.success,
                    content=getattr(result, 'content', None),
                    error=getattr(result, 'error', None),
                    execution_time=execution_time,
                    tool_name=tool_name
                )
            else:
                # Assume success if we got a result
                return ToolCallResult(
                    success=True,
                    content=result,
                    execution_time=execution_time,
                    tool_name=tool_name
                )
                
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            return ToolCallResult(
                success=False,
                error=f"Tool call timed out after {timeout} seconds",
                execution_time=execution_time,
                tool_name=tool_name
            )
        except Exception as e:
            execution_time = time.time() - start_time
            return ToolCallResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                tool_name=tool_name,
                traceback=traceback.format_exc()
            )
    
    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get available tools."""
        return self._available_tools.copy()
    
    async def cleanup(self):
        """Clean up HTTP client."""
        try:
            if self._client and hasattr(self._client, 'cleanup'):
                await self._client.cleanup()
        except Exception as e:
            logger.error(f"Error during HTTP client cleanup: {e}")
        finally:
            self._connected = False
            self._client = None
    
    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Debug callback for HTTP client."""
        logger.log(getattr(logging, level.upper(), logging.INFO), f"HTTP: {message}")
        if data and logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"  Data: {data}")


class SSEMCPClient(MCPClientInterface):
    """SSE protocol MCP client wrapper."""

    def __init__(self, anthropic_api_key: Optional[str] = None):
        self.anthropic_api_key = anthropic_api_key
        self._client = None
        self._available_tools = []
        self._connected = False
    
    @property
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self._connected and self._client is not None

    async def connect(self, url: str) -> bool:
        """Connect to SSE MCP server."""
        try:
            # Try different import paths
            original_path = sys.path.copy()
            try:
                sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
                from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib
            except ImportError:
                try:
                    from .mcp_sse_clientlib import MCPClientLib
                except ImportError:
                    try:
                        from mcp_sse_clientlib import MCPClientLib
                    except ImportError:
                        logger.error("Could not import MCPClientLib for SSE protocol")
                        return False
            finally:
                sys.path = original_path

            self._client = MCPClientLib(
                anthropic_api_key=self.anthropic_api_key,
                debug_callback=self._debug_callback
            )

            success = await self._client.connect_to_server(url)
            if success:
                self._available_tools = getattr(self._client, 'available_tools', [])
                self._connected = True
            return success

        except Exception as e:
            logger.error(f"SSE client connection error: {e}")
            logger.debug(traceback.format_exc())
            return False

    async def call_tool(self, tool_name: str, tool_input: Any, tool_call_id: str = "call", timeout: float = 30.0) -> ToolCallResult:
        """Call tool via SSE client."""
        if not self._client:
            return ToolCallResult(
                success=False,
                error="Client not connected",
                tool_name=tool_name
            )

        start_time = time.time()
        try:
            result = await asyncio.wait_for(
                self._client.call_tool(tool_name, tool_input, tool_call_id),
                timeout=timeout
            )
            
            execution_time = time.time() - start_time
            
            # Handle different result formats
            if hasattr(result, 'success'):
                return ToolCallResult(
                    success=result.success,
                    content=getattr(result, 'content', None),
                    error=getattr(result, 'error', None),
                    execution_time=execution_time,
                    tool_name=tool_name
                )
            else:
                # Assume success if we got a result
                return ToolCallResult(
                    success=True,
                    content=result,
                    execution_time=execution_time,
                    tool_name=tool_name
                )
                
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            return ToolCallResult(
                success=False,
                error=f"Tool call timed out after {timeout} seconds",
                execution_time=execution_time,
                tool_name=tool_name
            )
        except Exception as e:
            execution_time = time.time() - start_time
            return ToolCallResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                tool_name=tool_name,
                traceback=traceback.format_exc()
            )
    
    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get available tools."""
        return self._available_tools.copy()
    
    async def cleanup(self):
        """Clean up SSE client."""
        try:
            if self._client and hasattr(self._client, 'cleanup'):
                await self._client.cleanup()
        except Exception as e:
            logger.error(f"Error during SSE client cleanup: {e}")
        finally:
            self._connected = False
            self._client = None
    
    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Debug callback for SSE client."""
        logger.log(getattr(logging, level.upper(), logging.INFO), f"SSE: {message}")
        if data and logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"  Data: {data}")


class StdioMCPClient(MCPClientInterface):
    """Stdio protocol MCP client wrapper."""
    
    def __init__(self, command: str, args: List[str], env: Dict[str, str] = None):
        self.command = command
        self.args = args or []
        self.env = env or {}
        self._session = None
        self._available_tools = []
        self._exit_stack = None
        self._connected = False
        self._cleanup_lock = asyncio.Lock()
    
    @property
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self._connected and self._session is not None
    
    async def connect(self, url: str = None) -> bool:
        temp_exit_stack = None
        try:
            # Import MCP stdio components
            try:
                from mcp import ClientSession, StdioServerParameters
                from mcp.client.stdio import stdio_client
            except ImportError:
                logger.error("Could not import MCP stdio components")
                return False
            
            # Prepare environment
            process_env = os.environ.copy()
            process_env.update(self.env)
            
            # Create server parameters
            server_params = StdioServerParameters(
                command=self.command,
                args=self.args,
                env=process_env
            )
            
            # Create exit stack for resource management
            temp_exit_stack = AsyncExitStack()
            
            # Create stdio client and session
            stdio_transport = await temp_exit_stack.enter_async_context(
                stdio_client(server_params)
            )
            read_stream, write_stream = stdio_transport
            temp_session = await temp_exit_stack.enter_async_context(
                ClientSession(read_stream, write_stream)
            )
            
            # Initialize session
            await temp_session.initialize()
            
            # Get available tools
            tools_result = await temp_session.list_tools()
            self._available_tools = [
                {
                    'name': tool.name,
                    'description': getattr(tool, 'description', '') or '',
                    'inputSchema': getattr(tool, 'inputSchema', {})
                }
                for tool in tools_result.tools
            ]
            
            # Only assign to instance variables after successful initialization
            self._exit_stack = temp_exit_stack
            self._session = temp_session
            self.server_params = server_params
            self._connected = True
            
            return True
            
        except Exception as e:
            logger.error(f"Stdio client connection error: {e}")
            logger.debug(traceback.format_exc())
            # Clean up temporary resources
            if temp_exit_stack:
                try:
                    await temp_exit_stack.aclose()
                except Exception as cleanup_error:
                    logger.error(f"Error during cleanup: {cleanup_error}")
            return False
    
    async def call_tool(self, tool_name: str, tool_input: Any, tool_call_id: str = "call", timeout: float = 30.0) -> ToolCallResult:
        """Call tool via stdio session."""
        if not self._session:
            return ToolCallResult(
                success=False,
                error="Session not connected",
                tool_name=tool_name
            )
        
        start_time = time.time()
        
        try:
            # Prepare arguments
            if isinstance(tool_input, str):
                arguments = {"input": tool_input}
            elif isinstance(tool_input, dict):
                arguments = tool_input
            else:
                arguments = {"input": str(tool_input)}
            
            # Call the tool with timeout
            result = await asyncio.wait_for(
                self._session.call_tool(tool_name, arguments),
                timeout=timeout
            )
            
            execution_time = time.time() - start_time
            
            # Extract content
            content = self._extract_content(result)
            
            return ToolCallResult(
                success=True,
                content=content,
                execution_time=execution_time,
                tool_name=tool_name
            )
            
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            return ToolCallResult(
                success=False,
                error=f"Tool call timed out after {timeout} seconds",
                execution_time=execution_time,
                tool_name=tool_name
            )
        except Exception as e:
            execution_time = time.time() - start_time
            return ToolCallResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                tool_name=tool_name,
                traceback=traceback.format_exc()
            )
    
    def _extract_content(self, result) -> Any:
        """Extract content from MCP result."""
        try:
            if hasattr(result, 'content') and result.content:
                content_items = result.content
                if isinstance(content_items, list) and len(content_items) > 0:
                    first_item = content_items[0]
                    if hasattr(first_item, 'text'):
                        return first_item.text
                    elif hasattr(first_item, 'data'):
                        return first_item.data
                    else:
                        return str(first_item)
                else:
                    return str(content_items) if content_items else ""
            
            if hasattr(result, 'text'):
                return result.text
            elif hasattr(result, 'data'):
                return result.data
            else:
                return str(result) if result else ""
        except Exception as e:
            logger.error(f"Error extracting content: {e}")
            return str(result) if result else ""
    
    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get available tools."""
        return self._available_tools.copy()
    
    async def cleanup(self):
        """Clean up stdio client."""
        async with self._cleanup_lock:
            try:
                if self._exit_stack:
                    await self._exit_stack.aclose()
            except Exception as e:
                logger.error(f"Error during stdio client cleanup: {e}")
            finally:
                self._connected = False
                self._session = None
                self._exit_stack = None


class ClientFactory:
    """Factory for creating MCP clients based on protocol."""
    
    @staticmethod
    def create_client(protocol: Protocol, **kwargs) -> MCPClientInterface:
        """Create appropriate client for the given protocol."""
        if protocol == Protocol.HTTP:
            return HTTPMCPClient(kwargs.get('anthropic_api_key'))
        elif protocol == Protocol.SSE:
            return SSEMCPClient(kwargs.get('anthropic_api_key'))
        elif protocol == Protocol.STDIO:
            return StdioMCPClient(
                command=kwargs['command'],
                args=kwargs.get('args', []),
                env=kwargs.get('env', {})
            )
        else:
            raise ValueError(f"Unsupported protocol: {protocol}")


class ConnectionManager:
    """Manages MCP server connections."""
    
    def __init__(self):
        self._connections: Dict[str, ServerInfo] = {}
        self._lock = asyncio.Lock()
    
    async def add_connection(self, server_id: str, url: str, protocol: Protocol, 
                           description: str = "", **client_kwargs) -> bool:
        """Add a new server connection."""
        async with self._lock:
            if server_id in self._connections:
                logger.warning(f"Server {server_id} already exists")
                return False
        
        try:
            # Create client
            client = ClientFactory.create_client(protocol, **client_kwargs)
            
            # Connect
            success = await client.connect(url)
            if not success:
                return False
            
            # Get tools
            tools = await client.get_available_tools()
            
            # Store connection info
            async with self._lock:
                self._connections[server_id] = ServerInfo(
                    server_id=server_id,
                    url=url,
                    protocol=protocol,
                    description=description,
                    connected_at=datetime.now(),
                    tools=tools,
                    client=client,
                    metadata={
                        'command': client_kwargs.get('command'),
                        'args': client_kwargs.get('args'),
                        'env': client_kwargs.get('env')
                    }
                )
            
            tool_names = [tool['name'] for tool in tools]
            logger.info(f"Connected to {server_id} ({protocol.value}) with {len(tools)} tools: {', '.join(tool_names[:5])}{'...' if len(tool_names) > 5 else ''}")
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to {server_id}: {e}")
            logger.debug(traceback.format_exc())
            # Clean up if client was created
            if 'client' in locals():
                await client.cleanup()
            return False
    
    async def remove_connection(self, server_id: str) -> bool:
        # Get server info under lock
        async with self._lock:
            if server_id not in self._connections:
                return False
            server_info = self._connections[server_id]
            # Remove from connections immediately to prevent other operations
            del self._connections[server_id]
        
        # Cleanup outside of lock to prevent deadlock
        cleanup_success = True
        try:
            await server_info.client.cleanup()
            logger.info(f"Removed server: {server_id}")
        except Exception as e:
            logger.error(f"Error during cleanup for server {server_id}: {e}")
            logger.debug(traceback.format_exc())
            cleanup_success = False
            
            # Re-add to connections if cleanup failed
            async with self._lock:
                self._connections[server_id] = server_info
        
        return cleanup_success
    
    async def get_connection(self, server_id: str) -> Optional[ServerInfo]:
        """Get connection info for a server."""
        async with self._lock:
            return self._connections.get(server_id)
    
    async def list_connections(self) -> Dict[str, ServerInfo]:
        """List all connections."""
        async with self._lock:
            return self._connections.copy()
    
    async def cleanup_all(self):
        async with self._lock:
            server_ids = list(self._connections.keys())
        
        # Collect all cleanup tasks
        cleanup_tasks = []
        for server_id in server_ids:
            cleanup_tasks.append(self.remove_connection(server_id))
        
        # Execute all cleanups concurrently, handling failures individually
        if cleanup_tasks:
            results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            # Log any cleanup failures
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to cleanup server {server_ids[i]}: {result}")


class ToolRouter:
    """Routes tool calls to appropriate servers."""

    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager

    async def find_tool_server(self, tool_name: str) -> Optional[str]:
        """Find which server has a specific tool."""
        connections = await self.connection_manager.list_connections()
        for server_id, server_info in connections.items():
            tool_names = [tool['name'] for tool in server_info.tools]
            if tool_name in tool_names:
                return server_id
        return None

    async def get_all_tools(self) -> Dict[str, List[str]]:
        """Get all available tools grouped by server."""
        connections = await self.connection_manager.list_connections()
        return {
            server_id: [tool['name'] for tool in server_info.tools]
            for server_id, server_info in connections.items()
        }

    async def get_aggregated_tools(self) -> List[Dict[str, Any]]:
        """Get all tools from all servers with server metadata."""
        aggregated_tools = []
        connections = await self.connection_manager.list_connections()

        for server_id, server_info in connections.items():
            for tool in server_info.tools:
                tool_with_metadata = tool.copy()
                tool_with_metadata['_server_id'] = server_id
                tool_with_metadata['_server_url'] = server_info.url
                tool_with_metadata['_protocol'] = server_info.protocol.value
                aggregated_tools.append(tool_with_metadata)

        return aggregated_tools


class MultiMCPClient:
    """Clean, well-organized multi-MCP client."""

    def __init__(self, anthropic_api_key: Optional[str] = None, default_timeout: float = 120.0):
        """Initialize the multi-MCP client.

        Args:
            anthropic_api_key: Optional Anthropic API key for LLM operations
            default_timeout: Default timeout for tool calls in seconds
        """
        self.anthropic_api_key = anthropic_api_key or os.getenv('ANTHROPIC_API_KEY')
        self.default_timeout = default_timeout
        self.connection_manager = ConnectionManager()
        self.tool_router = ToolRouter(self.connection_manager)

        # Backward compatibility: maintain connections as a real attribute
        self.connections: Dict[str, Dict[str, Any]] = {}
        self._update_lock = asyncio.Lock()

    async def _update_connections_dict(self):
        async with self._update_lock:
            # Build new dict first, then replace atomically
            new_connections = {}
            connections = await self.connection_manager.list_connections()
            
            for server_id, server_info in connections.items():
                connection_dict = {
                    'client': server_info.client,
                    'url': server_info.url,
                    'protocol': server_info.protocol.value,
                    'description': server_info.description,
                    'connected_at': server_info.connected_at,
                    'tools': server_info.tools,
                    'session': getattr(server_info.client, '_session', None),
                    'process_params': getattr(server_info.client, 'server_params', None)
                }
                new_connections[server_id] = connection_dict
            
            # Atomic replacement
            self.connections = new_connections

    # Connection Management Methods

    async def add_server(self, server_id: str, server_url: str, protocol: str = "http",
                        description: str = "") -> bool:
        # Use a per-server lock to prevent concurrent operations on same server
        if not hasattr(self, '_server_locks'):
            self._server_locks = {}
        
        if server_id not in self._server_locks:
            self._server_locks[server_id] = asyncio.Lock()
        
        async with self._server_locks[server_id]:
            try:
                protocol_enum = Protocol.from_string(protocol)
                success = await self.connection_manager.add_connection(
                    server_id=server_id,
                    url=server_url,
                    protocol=protocol_enum,
                    description=description,
                    anthropic_api_key=self.anthropic_api_key
                )
                if success:
                    await self._update_connections_dict()
                return success
            except ValueError as e:
                logger.error(f"Invalid protocol '{protocol}': {e}")
                return False
            except Exception as e:
                logger.error(f"Error adding server {server_id}: {e}")
                logger.debug(traceback.format_exc())
                return False

    async def add_server_process(self, server_id: str, command: str, args: List[str] = None,
                               env: Dict[str, str] = None, description: str = "") -> bool:
        """Add and connect to an MCP server by spawning a process.

        Args:
            server_id: Unique identifier for this server
            command: Command to run (e.g., "npx", "python")
            args: Arguments for the command
            env: Environment variables to set
            description: Optional description of the server

        Returns:
            True if spawning and connection was successful
        """
        try:
            success = await self.connection_manager.add_connection(
                server_id=server_id,
                url=f"stdio://{command}",
                protocol=Protocol.STDIO,
                description=description,
                command=command,
                args=args or [],
                env=env or {}
            )
            if success:
                await self._update_connections_dict()
            return success
        except Exception as e:
            logger.error(f"Error adding server process {server_id}: {e}")
            logger.debug(traceback.format_exc())
            return False

    # Backward compatibility methods

    async def spawn_server(self, server_id: str, command: str, args: List[str] = None,
                          env: Dict[str, str] = None, description: str = "") -> bool:
        """Backward compatibility method for spawning servers."""
        return await self.add_server_process(server_id, command, args, env, description)

    def debug_callback(self, level: str, message: str, data: Any = None):
        """Debug callback for backward compatibility."""
        logger.log(getattr(logging, level.upper(), logging.INFO), f"MCP: {message}")
        if data and level == "debug":
            logger.debug(f"  Data: {data}")

    async def remove_server(self, server_id: str) -> bool:
        """Remove a server connection."""
        success = await self.connection_manager.remove_connection(server_id)
        if success:
            await self._update_connections_dict()
        return success

    async def list_servers(self) -> Dict[str, Dict[str, Any]]:
        """List all connected servers and their information."""
        result = {}
        connections = await self.connection_manager.list_connections()
        
        for server_id, server_info in connections.items():
            result[server_id] = {
                'url': server_info.url,
                'protocol': server_info.protocol.value,
                'description': server_info.description,
                'connected_at': server_info.connected_at.isoformat(),
                'tools': [tool['name'] for tool in server_info.tools],
                'tool_count': len(server_info.tools),
                'is_connected': server_info.client.is_connected
            }
        return result

    # Tool Management Methods

    async def get_all_tools(self) -> Dict[str, List[str]]:
        """Get all available tools grouped by server."""
        return await self.tool_router.get_all_tools()

    async def get_aggregated_tools(self) -> List[Dict[str, Any]]:
        """Get all tools from all servers with metadata."""
        return await self.tool_router.get_aggregated_tools()

    async def find_tool_server(self, tool_name: str) -> Optional[str]:
        """Find which server has a specific tool."""
        return await self.tool_router.find_tool_server(tool_name)

    # Tool Execution Methods

    async def call_tool(self, server_id: str, tool_name: str, tool_input: Any,
                       tool_call_id: str = "call", timeout: Optional[float] = None) -> Dict[str, Any]:
        """Call a tool on a specific server.

        Args:
            server_id: ID of the server to call the tool on
            tool_name: Name of the tool to call
            tool_input: Input for the tool
            tool_call_id: ID for tracking the tool call
            timeout: Optional timeout override (defaults to self.default_timeout)

        Returns:
            Dictionary with execution details (backward compatible format)
        """
        timeout = timeout or self.default_timeout
        
        server_info = await self.connection_manager.get_connection(server_id)
        if not server_info:
            return {
                'success': False,
                'error': f"Server {server_id} not connected",
                'server_id': server_id,
                'tool_name': tool_name
            }

        try:
            logger.info(f"Calling tool {tool_name} on {server_id} (timeout: {timeout}s)")
            result = await server_info.client.call_tool(tool_name, tool_input, tool_call_id, timeout)

            # Convert ToolCallResult to dictionary format for backward compatibility
            return {
                'success': result.success,
                'content': result.content,
                'error': result.error,
                'server_id': server_id,
                'tool_name': tool_name,
                'execution_time': result.execution_time,
                'traceback': result.traceback
            }

        except Exception as e:
            logger.error(f"Error calling tool {tool_name} on {server_id}: {e}")
            logger.debug(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'server_id': server_id,
                'tool_name': tool_name,
                'traceback': traceback.format_exc()
            }

    async def call_tool_auto(self, tool_name: str, tool_input: Any,
                           tool_call_id: str = "auto_call", timeout: Optional[float] = None) -> Dict[str, Any]:
        """Automatically find and call a tool on the appropriate server.

        Args:
            tool_name: Name of the tool to call
            tool_input: Input for the tool
            tool_call_id: ID for tracking the tool call
            timeout: Optional timeout override

        Returns:
            Dictionary with execution details (backward compatible format)
        """
        server_id = await self.find_tool_server(tool_name)
        if not server_id:
            return {
                'success': False,
                'error': f"Tool {tool_name} not found on any connected server",
                'tool_name': tool_name
            }

        return await self.call_tool(server_id, tool_name, tool_input, tool_call_id, timeout)

    # Query Processing Methods

    async def process_query(self, query: str, server_id: Optional[str] = None,
                          **kwargs) -> Dict[str, Any]:
        """Process a query using a specific server or the first available server.

        Args:
            query: The query to process
            server_id: Optional server ID. If not provided, uses first available server
            **kwargs: Additional arguments for query processing

        Returns:
            Query result dictionary
        """
        target_server = server_id
        if not target_server:
            connections = await self.connection_manager.list_connections()
            if not connections:
                return {'error': 'No servers connected'}
            target_server = next(iter(connections.keys()))

        server_info = await self.connection_manager.get_connection(target_server)
        if not server_info:
            return {'error': f'Server {target_server} not connected'}

        try:
            # Check if the client supports query processing
            if hasattr(server_info.client, 'process_query'):
                # Log LLM query start with timing
                query_start_time = time.time()
                logger.info(f"🤖 [SERVER] Starting LLM query processing - Server: {target_server}, Query: {query[:100]}{'...' if len(query) > 100 else ''}")

                result = await server_info.client.process_query(query, **kwargs)

                # Log LLM query completion with timing
                query_end_time = time.time()
                query_latency = query_end_time - query_start_time
                success_indicator = "✅" if not result.get('error') else "❌"
                logger.info(f"{success_indicator} [SERVER] LLM query completed - Server: {target_server}, Total time: {query_latency:.3f}s")

                result['server_id'] = target_server
                return result
            else:
                return {'error': f'Server {target_server} does not support query processing'}

        except Exception as e:
            # Log error with timing if query_start_time was set
            if 'query_start_time' in locals():
                query_end_time = time.time()
                query_latency = query_end_time - query_start_time
                logger.error(f"❌ [SERVER] LLM query failed - Server: {target_server}, Total time: {query_latency:.3f}s, Error: {e}")
            else:
                logger.error(f"❌ [SERVER] LLM query failed - Server: {target_server}, Error: {e}")
            logger.debug(traceback.format_exc())
            return {
                'error': str(e),
                'server_id': target_server,
                'traceback': traceback.format_exc()
            }

    # Cleanup Methods

    async def cleanup(self):
        """Clean up all connections and resources."""
        logger.info("Cleaning up MultiMCPClient...")
        await self.connection_manager.cleanup_all()
        logger.info("MultiMCPClient cleanup complete")


# Example usage and testing
async def example_usage():
    """Example demonstrating clean multi-MCP client usage."""
    client = MultiMCPClient()

    try:
        # Add servers
        print("Connecting to servers...")

        # HTTP server
        success = await client.add_server(
            server_id="gaia_http",
            server_url="http://localhost:9000/mcp",
            protocol="http",
            description="Local Gaia HTTP server"
        )
        
        if success:
            print("✅ Connected to HTTP server")
        else:
            print("❌ Failed to connect to HTTP server")

        # List servers
        print("\nConnected servers:")
        servers = await client.list_servers()
        for server_id, info in servers.items():
            print(f"  {server_id}: {info['description']} ({info['tool_count']} tools)")

        # List tools
        print("\nAvailable tools:")
        all_tools = await client.get_all_tools()
        for server_id, tools in all_tools.items():
            print(f"  {server_id}: {', '.join(tools[:5])}{'...' if len(tools) > 5 else ''}")

        # Call a tool
        print("\nCalling echostring tool...")
        result = await client.call_tool_auto("echostring", "Hello from clean client!")

        if result['success']:
            print(f"✅ Result: {result['content']}")
        else:
            print(f"❌ Error: {result['error']}")

    except Exception as e:
        print(f"Error: {e}")
        logger.debug(traceback.format_exc())
    finally:
        await client.cleanup()


if __name__ == "__main__":
    asyncio.run(example_usage())